"""
Simple test for unified addon loading to verify single source of truth
"""
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from erp.database.memory.app_registry import AppRegistry


def test_unified_addon_loading_state_management():
    """Test that addon loading state is properly managed"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        # Initial state should be NOT_STARTED
        state = await registry.get_addon_loading_state()
        assert state == "NOT_STARTED"
        
        # Set up mock loading order
        async with registry._lock:
            registry._addon_load_order = ['addon1', 'addon2']
        
        # Mock the unified loading methods
        with patch.object(registry, '_register_addon_paths_unified', new_callable=AsyncMock) as mock_register:
            with patch.object(registry, '_import_addon_modules_unified', new_callable=AsyncMock) as mock_import:
                with patch.object(registry, '_discover_routes_unified', new_callable=AsyncMock) as mock_routes:
                    
                    # Execute unified loading
                    await registry._load_all_addons_unified()
                    
                    # Verify all methods were called
                    mock_register.assert_called_once()
                    mock_import.assert_called_once()
                    mock_routes.assert_called_once()
                    
                    # Verify final state
                    state = await registry.get_addon_loading_state()
                    assert state == "LOADED"
                    
                    # Reset mocks
                    mock_register.reset_mock()
                    mock_import.reset_mock()
                    mock_routes.reset_mock()
                    
                    # Second call should not execute loading again
                    await registry._load_all_addons_unified()
                    
                    # Verify methods were NOT called again
                    mock_register.assert_not_called()
                    mock_import.assert_not_called()
                    mock_routes.assert_not_called()
    
    asyncio.run(run_test())


def test_addon_loading_failure_handling():
    """Test that addon loading failures are properly handled"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        # Set up mock loading order
        async with registry._lock:
            registry._addon_load_order = ['addon1', 'addon2']
        
        # Mock a failure in import
        with patch.object(registry, '_register_addon_paths_unified', new_callable=AsyncMock):
            with patch.object(registry, '_import_addon_modules_unified', new_callable=AsyncMock, side_effect=Exception("Import failed")):
                with patch.object(registry, '_discover_routes_unified', new_callable=AsyncMock):
                    
                    # Attempt loading (should fail)
                    try:
                        await registry._load_all_addons_unified()
                        assert False, "Expected exception was not raised"
                    except Exception as e:
                        assert str(e) == "Import failed"
                    
                    # Verify failure state
                    state = await registry.get_addon_loading_state()
                    assert state == "FAILED"
    
    asyncio.run(run_test())


def test_loaded_addons_tracking():
    """Test that loaded addons are properly tracked"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        # Set up mock loading order
        async with registry._lock:
            registry._addon_load_order = ['addon1', 'addon2']
        
        # Mock sys.modules to simulate loaded modules
        with patch('sys.modules', {'erp.addons.addon1': Mock(), 'erp.addons.addon2': Mock()}):
            with patch('importlib.import_module') as mock_import:
                with patch.object(registry, '_import_addon_modules_recursive', new_callable=AsyncMock):
                    with patch('os.path.exists', return_value=True):
                        with patch('os.path.isdir', return_value=True):
                            with patch('erp.config.config') as mock_config:
                                mock_config.addons_paths = ['addons']

                                # Execute import
                                await registry._import_addon_modules_unified()

                                # Verify loaded addons tracking
                                loaded_addons = await registry.get_loaded_addons()
                                assert loaded_addons == {'addon1', 'addon2'}
    
    asyncio.run(run_test())


def test_deprecated_methods_warning():
    """Test that deprecated methods issue warnings"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        with patch.object(registry._logger, 'warning') as mock_warning:
            # Test deprecated _ensure_addon_paths_registered
            await registry._ensure_addon_paths_registered(['addon1'])
            mock_warning.assert_called_with("DEPRECATED: _ensure_addon_paths_registered called for test_db. Use unified loading instead.")
    
    asyncio.run(run_test())


def test_route_manager_integration():
    """Test integration with RouteManager unified approach"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        # Mock route manager
        mock_route_manager = Mock()
        mock_route_manager.discover_routes_from_loaded_addons = AsyncMock()
        registry.route_manager = mock_route_manager
        
        # Set up loaded addons
        async with registry._lock:
            registry._loaded_addons = {'addon1', 'addon2'}
        
        # Execute route discovery
        await registry._discover_routes_unified()
        
        # Verify route manager was called with loaded addons
        mock_route_manager.discover_routes_from_loaded_addons.assert_called_once_with(['addon1', 'addon2'])
    
    asyncio.run(run_test())


def test_ensure_routes_registered_integration():
    """Test that ensure_routes_registered works with unified loading"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        with patch.object(registry, '_load_all_addons_unified', new_callable=AsyncMock) as mock_unified_loading:
            
            # Case 1: Not started - should trigger unified loading
            async with registry._lock:
                registry._addon_loading_state = "NOT_STARTED"
            
            await registry.ensure_routes_registered()
            mock_unified_loading.assert_called_once()
            
            # Case 2: Already loaded - should not trigger loading
            mock_unified_loading.reset_mock()
            async with registry._lock:
                registry._addon_loading_state = "LOADED"
            
            await registry.ensure_routes_registered()
            mock_unified_loading.assert_not_called()
    
    asyncio.run(run_test())


def test_no_redundant_loading_calls():
    """Test that redundant loading methods are not called during unified loading"""
    
    async def run_test():
        registry = AppRegistry("test_db")
        
        # Mock database manager
        mock_db_manager = Mock()
        mock_db_manager.fetch = AsyncMock(return_value=[
            {'name': 'base', 'display_name': 'Base', 'version': '1.0', 'state': 'installed', 'action_at': '2024-01-01', 'create_at': '2024-01-01'},
            {'name': 'addon1', 'display_name': 'Addon 1', 'version': '1.0', 'state': 'installed', 'action_at': '2024-01-02', 'create_at': '2024-01-02'},
        ])
        
        with patch('erp.database.registry.database_registry.DatabaseRegistry.get_database', return_value=mock_db_manager):
            with patch.object(registry.model_metadata_manager, 'load_models_metadata', new_callable=AsyncMock):
                with patch.object(registry.model_metadata_manager, 'load_fields_metadata', new_callable=AsyncMock):
                    with patch.object(registry.addon_manager, 'load_addons_in_order', new_callable=AsyncMock) as mock_old_loading:
                        with patch.object(registry.addon_manager, 'import_addon_modules', new_callable=AsyncMock) as mock_old_import:
                            with patch.object(registry, '_register_addon_paths_unified', new_callable=AsyncMock):
                                with patch.object(registry, '_import_addon_modules_unified', new_callable=AsyncMock):
                                    with patch.object(registry, '_discover_routes_unified', new_callable=AsyncMock):
                                        
                                        # Execute refresh_from_database
                                        await registry.refresh_from_database()
                                        
                                        # Verify old methods were NOT called
                                        mock_old_loading.assert_not_called()
                                        mock_old_import.assert_not_called()
                                        
                                        # Verify state is LOADED
                                        state = await registry.get_addon_loading_state()
                                        assert state == "LOADED"
    
    asyncio.run(run_test())


if __name__ == "__main__":
    test_unified_addon_loading_state_management()
    test_addon_loading_failure_handling()
    test_loaded_addons_tracking()
    test_deprecated_methods_warning()
    test_route_manager_integration()
    test_ensure_routes_registered_integration()
    test_no_redundant_loading_calls()
    print("All tests passed!")
