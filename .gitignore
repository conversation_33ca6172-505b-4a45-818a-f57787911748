# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Database files
*.log
db.sqlite3
db.sqlite3-journal

# Documentation builds
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is generally recommended to include the cache and path variables, but
#  exclude the .idea/ folder, and VCS and deployment information.
.idea/

# ERP-specific files
# Log files
*.log
erp.log
logs/
log/

# Organized directory structure
# Temporary files that might be created in root
cluster.id
*.tmp
*.temp

# Scripts directory - ignore generated activation scripts
scripts/activate_env.sh

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/local.conf
config/production.conf
config/development.conf
config/*.local.conf
*.secret.conf

# Session files
sessions/
*.session

# File uploads and attachments
uploads/
attachments/
filestore/
static/uploads/

# Backup files
*.bak
*.backup
backups/
dump/
*.sql.gz
*.dump

# Cache directories
cache/
.cache/
tmp/
temp/

# Addon-specific ignores
addons/*/static/compiled/
addons/*/i18n/*.pot

# Development and testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if using for frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Compiled CSS and JS
*.min.css
*.min.js
static/compiled/

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local development overrides
docker-compose.local.yml
.env.local
.env.development
.env.production

# Migration files (uncomment if you want to ignore them)
# migrations/

# Custom local files
local/
*.local