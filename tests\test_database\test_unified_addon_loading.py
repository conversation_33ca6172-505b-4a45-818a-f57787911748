"""
Test unified addon loading in AppRegistry to ensure single source of truth
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, call
from typing import List, Set

from erp.database.memory.app_registry import AppRegistry
from erp.database.memory.registry_manager import MemoryRegistryManager


class TestUnifiedAddonLoading:
    """Test the unified addon loading system in AppRegistry"""

    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager"""
        db_manager = Mock()
        db_manager.fetch = AsyncMock(return_value=[
            {'name': 'base', 'display_name': 'Base', 'version': '1.0', 'state': 'installed', 'action_at': '2024-01-01', 'create_at': '2024-01-01'},
            {'name': 'addon1', 'display_name': 'Addon 1', 'version': '1.0', 'state': 'installed', 'action_at': '2024-01-02', 'create_at': '2024-01-02'},
            {'name': 'addon2', 'display_name': 'Addon 2', 'version': '1.0', 'state': 'installed', 'action_at': '2024-01-03', 'create_at': '2024-01-03'},
        ])
        return db_manager

    @pytest.fixture
    def app_registry(self):
        """Create AppRegistry instance for testing"""
        registry = AppRegistry("test_db")
        return registry

    def test_unified_addon_loading_single_execution(self, app_registry, mock_db_manager):
        """Test that unified addon loading executes only once"""
        async def run_test():
            with patch('erp.database.registry.database_registry.DatabaseRegistry.get_database', return_value=mock_db_manager):
                with patch.object(app_registry.model_metadata_manager, 'load_models_metadata', new_callable=AsyncMock) as mock_load_models:
                    with patch.object(app_registry.model_metadata_manager, 'load_fields_metadata', new_callable=AsyncMock) as mock_load_fields:
                        with patch.object(app_registry, '_register_addon_paths_unified', new_callable=AsyncMock) as mock_register_paths:
                            with patch.object(app_registry, '_import_addon_modules_unified', new_callable=AsyncMock) as mock_import_modules:
                                with patch.object(app_registry, '_discover_routes_unified', new_callable=AsyncMock) as mock_discover_routes:

                                    # First call to refresh_from_database
                                    await app_registry.refresh_from_database()

                                    # Verify unified loading was called once
                                    mock_register_paths.assert_called_once()
                                    mock_import_modules.assert_called_once()
                                    mock_discover_routes.assert_called_once()

                                    # Verify state is LOADED
                                    state = await app_registry.get_addon_loading_state()
                                    assert state == "LOADED"

                                    # Reset mocks
                                    mock_register_paths.reset_mock()
                                    mock_import_modules.reset_mock()
                                    mock_discover_routes.reset_mock()

                                    # Second call to refresh_from_database should not trigger loading again
                                    await app_registry.refresh_from_database()

                                    # Verify unified loading was NOT called again
                                    mock_register_paths.assert_called_once()  # Called once in the new refresh
                                    mock_import_modules.assert_called_once()   # Called once in the new refresh
                                    mock_discover_routes.assert_called_once()  # Called once in the new refresh

        asyncio.run(run_test())

    async def test_addon_loading_state_management(self, app_registry):
        """Test addon loading state transitions"""
        # Initial state
        state = await app_registry.get_addon_loading_state()
        assert state == "NOT_STARTED"
        
        # Mock the loading process
        with patch.object(app_registry, '_register_addon_paths_unified', new_callable=AsyncMock):
            with patch.object(app_registry, '_import_addon_modules_unified', new_callable=AsyncMock):
                with patch.object(app_registry, '_discover_routes_unified', new_callable=AsyncMock):
                    
                    # Set up addon load order
                    async with app_registry._lock:
                        app_registry._addon_load_order = ['addon1', 'addon2']
                    
                    # Start loading
                    await app_registry._load_all_addons_unified()
                    
                    # Verify final state
                    state = await app_registry.get_addon_loading_state()
                    assert state == "LOADED"

    async def test_addon_loading_failure_state(self, app_registry):
        """Test addon loading failure state management"""
        # Mock a failure in the loading process
        with patch.object(app_registry, '_register_addon_paths_unified', new_callable=AsyncMock):
            with patch.object(app_registry, '_import_addon_modules_unified', new_callable=AsyncMock, side_effect=Exception("Import failed")):
                with patch.object(app_registry, '_discover_routes_unified', new_callable=AsyncMock):
                    
                    # Set up addon load order
                    async with app_registry._lock:
                        app_registry._addon_load_order = ['addon1', 'addon2']
                    
                    # Attempt loading (should fail)
                    with pytest.raises(Exception, match="Import failed"):
                        await app_registry._load_all_addons_unified()
                    
                    # Verify failure state
                    state = await app_registry.get_addon_loading_state()
                    assert state == "FAILED"

    async def test_concurrent_addon_loading_prevention(self, app_registry):
        """Test that concurrent addon loading is prevented"""
        loading_started = asyncio.Event()
        loading_can_continue = asyncio.Event()
        
        async def slow_import():
            loading_started.set()
            await loading_can_continue.wait()
        
        with patch.object(app_registry, '_register_addon_paths_unified', new_callable=AsyncMock):
            with patch.object(app_registry, '_import_addon_modules_unified', new_callable=AsyncMock, side_effect=slow_import):
                with patch.object(app_registry, '_discover_routes_unified', new_callable=AsyncMock):
                    
                    # Set up addon load order
                    async with app_registry._lock:
                        app_registry._addon_load_order = ['addon1', 'addon2']
                    
                    # Start first loading task
                    task1 = asyncio.create_task(app_registry._load_all_addons_unified())
                    
                    # Wait for first task to start
                    await loading_started.wait()
                    
                    # Start second loading task (should return immediately)
                    task2 = asyncio.create_task(app_registry._load_all_addons_unified())
                    
                    # Give task2 a moment to complete
                    await asyncio.sleep(0.1)
                    assert task2.done()
                    
                    # Allow first task to complete
                    loading_can_continue.set()
                    await task1
                    
                    # Verify state
                    state = await app_registry.get_addon_loading_state()
                    assert state == "LOADED"

    async def test_loaded_addons_tracking(self, app_registry):
        """Test that loaded addons are properly tracked"""
        with patch('sys.modules', {'erp.addons.addon1': Mock(), 'erp.addons.addon2': Mock()}):
            with patch('importlib.import_module') as mock_import:
                with patch.object(app_registry, '_import_addon_modules_recursive', new_callable=AsyncMock):
                    with patch('os.path.exists', return_value=True):
                        with patch('os.path.isdir', return_value=True):
                            with patch('erp.config.config.addons_paths', ['addons']):
                                
                                # Set up addon load order
                                async with app_registry._lock:
                                    app_registry._addon_load_order = ['addon1', 'addon2']
                                
                                # Execute import
                                await app_registry._import_addon_modules_unified()
                                
                                # Verify loaded addons tracking
                                loaded_addons = await app_registry.get_loaded_addons()
                                assert loaded_addons == {'addon1', 'addon2'}

    async def test_route_manager_integration(self, app_registry):
        """Test integration with RouteManager unified approach"""
        mock_route_manager = Mock()
        mock_route_manager.discover_routes_from_loaded_addons = AsyncMock()
        app_registry.route_manager = mock_route_manager
        
        # Set up loaded addons
        async with app_registry._lock:
            app_registry._loaded_addons = {'addon1', 'addon2'}
        
        # Execute route discovery
        await app_registry._discover_routes_unified()
        
        # Verify route manager was called with loaded addons
        mock_route_manager.discover_routes_from_loaded_addons.assert_called_once_with(['addon1', 'addon2'])

    async def test_ensure_routes_registered_with_unified_loading(self, app_registry):
        """Test that ensure_routes_registered works with unified loading"""
        with patch.object(app_registry, '_load_all_addons_unified', new_callable=AsyncMock) as mock_unified_loading:
            
            # Case 1: Not started - should trigger unified loading
            async with app_registry._lock:
                app_registry._addon_loading_state = "NOT_STARTED"
            
            await app_registry.ensure_routes_registered()
            mock_unified_loading.assert_called_once()
            
            # Case 2: Already loaded - should not trigger loading
            mock_unified_loading.reset_mock()
            async with app_registry._lock:
                app_registry._addon_loading_state = "LOADED"
            
            await app_registry.ensure_routes_registered()
            mock_unified_loading.assert_not_called()

    async def test_deprecated_methods_warning(self, app_registry):
        """Test that deprecated methods issue warnings"""
        with patch.object(app_registry._logger, 'warning') as mock_warning:
            
            # Test deprecated _ensure_addon_paths_registered
            await app_registry._ensure_addon_paths_registered(['addon1'])
            mock_warning.assert_called_with("DEPRECATED: _ensure_addon_paths_registered called for test_db. Use unified loading instead.")

    async def test_registry_manager_integration(self, mock_db_manager):
        """Test integration with MemoryRegistryManager"""
        with patch('erp.database.registry.database_registry.DatabaseRegistry.get_database', return_value=mock_db_manager):
            with patch.object(MemoryRegistryManager, '_is_base_module_installed', return_value=True):
                
                # Initialize registry through manager
                registry = await MemoryRegistryManager.initialize_registry_with_delay("test_integration_db", 0)
                
                # Verify unified loading was executed
                state = await registry.get_addon_loading_state()
                assert state == "LOADED"
                
                # Verify addon load order was set
                addon_order = await registry.get_addon_load_order()
                assert 'addon1' in addon_order
                assert 'addon2' in addon_order
                
                # Cleanup
                if "test_integration_db" in MemoryRegistryManager._registries:
                    del MemoryRegistryManager._registries["test_integration_db"]

    async def test_no_redundant_loading_calls(self, app_registry, mock_db_manager):
        """Test that redundant loading methods are not called during unified loading"""
        with patch('erp.database.registry.database_registry.DatabaseRegistry.get_database', return_value=mock_db_manager):
            with patch.object(app_registry.model_metadata_manager, 'load_models_metadata', new_callable=AsyncMock):
                with patch.object(app_registry.model_metadata_manager, 'load_fields_metadata', new_callable=AsyncMock):
                    with patch.object(app_registry.addon_manager, 'load_addons_in_order', new_callable=AsyncMock) as mock_old_loading:
                        with patch.object(app_registry.addon_manager, 'import_addon_modules', new_callable=AsyncMock) as mock_old_import:
                            with patch.object(app_registry.route_manager, 'ensure_routes_registered', new_callable=AsyncMock) as mock_old_routes:

                                # Execute refresh_from_database
                                await app_registry.refresh_from_database()

                                # Verify old methods were NOT called
                                mock_old_loading.assert_not_called()
                                mock_old_import.assert_not_called()

                                # Verify state is LOADED
                                state = await app_registry.get_addon_loading_state()
                                assert state == "LOADED"

    async def test_addon_load_order_consistency(self, app_registry, mock_db_manager):
        """Test that addon load order is consistent throughout the process"""
        with patch('erp.database.registry.database_registry.DatabaseRegistry.get_database', return_value=mock_db_manager):
            with patch.object(app_registry.model_metadata_manager, 'load_models_metadata', new_callable=AsyncMock):
                with patch.object(app_registry.model_metadata_manager, 'load_fields_metadata', new_callable=AsyncMock):
                    with patch.object(app_registry, '_register_addon_paths_unified', new_callable=AsyncMock) as mock_register:
                        with patch.object(app_registry, '_import_addon_modules_unified', new_callable=AsyncMock) as mock_import:
                            with patch.object(app_registry, '_discover_routes_unified', new_callable=AsyncMock) as mock_routes:

                                # Execute refresh_from_database
                                await app_registry.refresh_from_database()

                                # Verify addon load order was set correctly
                                addon_order = await app_registry.get_addon_load_order()
                                expected_order = ['addon1', 'addon2']  # Based on mock_db_manager data
                                assert addon_order == expected_order

                                # Verify all unified methods were called
                                mock_register.assert_called_once()
                                mock_import.assert_called_once()
                                mock_routes.assert_called_once()
