"""
In-memory registry system for databases
"""
from .app_registry import AppRegistry
from .registry_manager import MemoryRegistryManager
from .filter_processor import DatabaseFilterProcessor
from .cache_manager import CacheManager, QueryCache
from .addon_manager import AddonManager
from .model_metadata_manager import ModelMetadataManager
from .route_manager import <PERSON><PERSON><PERSON><PERSON>


def get_memory_registry_manager():
    """
    Get the MemoryRegistryManager class.
    This function provides a consistent interface for accessing the memory registry manager.

    Returns:
        MemoryRegistryManager: The memory registry manager class
    """
    return MemoryRegistryManager


__all__ = [
    'AppRegistry',
    'MemoryRegistryManager',
    'DatabaseFilterProcessor',
    'CacheManager',
    'QueryCache',
    'AddonManager',
    'ModelMetadataManager',
    'RouteManager',
    'get_memory_registry_manager'
]
